import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import PrintView from '../src/views/PrintView.vue'
import * as apiService from '../src/services/api.js'

// Mock the API service
vi.mock('../src/services/api.js', () => ({
  uploadCliFile: vi.fn(),
  getCliLayerPreview: vi.fn(),
  sendCliLayerToDrum: vi.fn(),
  startPrintJob: vi.fn(),
  cancelPrintJob: vi.fn(),
  setLayerParameters: vi.fn(),
  getLayerParameters: vi.fn(),
  getLayerPreview: vi.fn(),
  uploadDrumGeometry: vi.fn(),
  downloadDrumGeometry: vi.fn(),
  deleteDrumGeometry: vi.fn()
}))

// Mock the status store
const mockStatusStore = {
  isConnected: true,
  connectWebSocket: vi.fn(),
  disconnectWebSocket: vi.fn()
}

vi.mock('../src/stores/status.js', () => ({
  useStatusStore: () => mockStatusStore
}))

describe('PrintView CLI Layer Sending', () => {
  let wrapper

  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()

    wrapper = mount(PrintView, {
      global: {
        stubs: {
          'router-link': true
        }
      }
    })
  })

  it('renders CLI layer preview section when file is uploaded', async () => {
    // Simulate CLI file upload
    wrapper.vm.cliFileInfo = {
      file_id: 'test-file-id',
      total_layers: 5
    }
    await wrapper.vm.$nextTick()

    expect(wrapper.find('.layer-preview-section').exists()).toBe(true)
    expect(wrapper.text()).toContain('Total Layers: 5')
  })

  it('shows target drum selection when CLI file is uploaded', async () => {
    // Simulate CLI file upload
    wrapper.vm.cliFileInfo = {
      file_id: 'test-file-id',
      total_layers: 3
    }
    await wrapper.vm.$nextTick()

    const drumSelect = wrapper.find('#target-drum-select')
    expect(drumSelect.exists()).toBe(true)
    expect(drumSelect.findAll('option')).toHaveLength(4) // Empty option + 3 drums
  })

  it('shows send layer button when CLI file is uploaded', async () => {
    // Simulate CLI file upload
    wrapper.vm.cliFileInfo = {
      file_id: 'test-file-id',
      total_layers: 2
    }
    await wrapper.vm.$nextTick()

    const sendButton = wrapper.find('button:contains("Send Layer to Recoater")')
    expect(sendButton.exists()).toBe(true)
  })

  it('disables send button when no drum is selected', async () => {
    // Simulate CLI file upload
    wrapper.vm.cliFileInfo = {
      file_id: 'test-file-id',
      total_layers: 2
    }
    wrapper.vm.selectedLayerNum = 1
    wrapper.vm.selectedTargetDrumId = ''
    await wrapper.vm.$nextTick()

    const sendButton = wrapper.find('button:contains("Send Layer to Recoater")')
    expect(sendButton.attributes('disabled')).toBeDefined()
  })

  it('enables send button when all required fields are filled', async () => {
    // Simulate CLI file upload
    wrapper.vm.cliFileInfo = {
      file_id: 'test-file-id',
      total_layers: 2
    }
    wrapper.vm.selectedLayerNum = 1
    wrapper.vm.selectedTargetDrumId = '1'
    await wrapper.vm.$nextTick()

    const sendButton = wrapper.find('button:contains("Send Layer to Recoater")')
    expect(sendButton.attributes('disabled')).toBeUndefined()
  })

  it('calls API service when send layer button is clicked', async () => {
    // Mock successful API response
    apiService.sendCliLayerToDrum.mockResolvedValue({
      data: {
        success: true,
        message: 'Layer sent successfully'
      }
    })

    // Setup component state
    wrapper.vm.cliFileInfo = {
      file_id: 'test-file-id',
      total_layers: 3
    }
    wrapper.vm.selectedLayerNum = 2
    wrapper.vm.selectedTargetDrumId = '1'
    await wrapper.vm.$nextTick()

    // Click send button
    const sendButton = wrapper.find('button:contains("Send Layer to Recoater")')
    await sendButton.trigger('click')

    // Verify API was called with correct parameters
    expect(apiService.sendCliLayerToDrum).toHaveBeenCalledWith('test-file-id', 2, 1)
  })

  it('shows loading state when sending layer', async () => {
    // Mock API to return a pending promise
    let resolvePromise
    const pendingPromise = new Promise(resolve => {
      resolvePromise = resolve
    })
    apiService.sendCliLayerToDrum.mockReturnValue(pendingPromise)

    // Setup component state
    wrapper.vm.cliFileInfo = {
      file_id: 'test-file-id',
      total_layers: 2
    }
    wrapper.vm.selectedLayerNum = 1
    wrapper.vm.selectedTargetDrumId = '0'
    await wrapper.vm.$nextTick()

    // Click send button
    const sendButton = wrapper.find('button:contains("Send Layer to Recoater")')
    await sendButton.trigger('click')

    // Check loading state
    expect(wrapper.vm.isCliSendLoading).toBe(true)
    expect(sendButton.text()).toContain('Sending...')

    // Resolve the promise
    resolvePromise({ data: { success: true } })
    await wrapper.vm.$nextTick()

    // Check loading state is cleared
    expect(wrapper.vm.isCliSendLoading).toBe(false)
  })

  it('handles API errors when sending layer', async () => {
    // Mock API error
    const errorResponse = {
      response: {
        data: {
          detail: 'Connection error'
        }
      }
    }
    apiService.sendCliLayerToDrum.mockRejectedValue(errorResponse)

    // Setup component state
    wrapper.vm.cliFileInfo = {
      file_id: 'test-file-id',
      total_layers: 1
    }
    wrapper.vm.selectedLayerNum = 1
    wrapper.vm.selectedTargetDrumId = '2'
    await wrapper.vm.$nextTick()

    // Click send button
    const sendButton = wrapper.find('button:contains("Send Layer to Recoater")')
    await sendButton.trigger('click')

    // Wait for error handling
    await wrapper.vm.$nextTick()

    // Verify error handling
    expect(wrapper.vm.isCliSendLoading).toBe(false)
    // Note: We can't easily test the showMessage call without more complex mocking
  })
})
