/**
 * Tests for PrintView component
 * =============================
 * 
 * Comprehensive test suite for the PrintView component including
 * layer parameters management and preview functionality.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import PrintView from '../PrintView.vue'
import { useStatusStore } from '../../stores/status'
import apiService from '../../services/api'

// Mock API service
vi.mock('../../services/api', () => ({
  default: {
    getLayerParameters: vi.fn(),
    setLayerParameters: vi.fn(),
    getLayerPreview: vi.fn(),
    getDrumGeometryPreview: vi.fn(),
    startPrintJob: vi.fn(),
    cancelPrintJob: vi.fn(),
    getPrintJobStatus: vi.fn(),
    uploadDrumGeometry: vi.fn(),
    downloadDrumGeometry: vi.fn(),
    deleteDrumGeometry: vi.fn(),
    uploadCliFile: vi.fn(),
    getCliLayerPreview: vi.fn()
  }
}))

// Mock status store
vi.mock('../../stores/status', () => ({
  useStatusStore: vi.fn()
}))

describe('PrintView', () => {
  let mockStatusStore

  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()

    // Ensure document.body exists and is properly set up
    if (!document.body) {
      document.body = document.createElement('body')
    }

    // Clear any existing content
    document.body.innerHTML = ''

    // Mock URL APIs for blob handling
    global.URL = global.URL || {}
    global.URL.createObjectURL = vi.fn(() => 'blob:mock-url')
    global.URL.revokeObjectURL = vi.fn()

    // Mock confirm function
    global.confirm = vi.fn(() => true)

    // Set up default API mocks
    apiService.getLayerParameters.mockResolvedValue({
      data: {
        layer_height: 0.1,
        exposure_time: 8.0,
        bottom_exposure_time: 60.0,
        light_off_delay: 1.0,
        bottom_light_off_delay: 1.0,
        bottom_layer_count: 5,
        z_lift_distance: 5.0,
        z_lift_speed: 3.0,
        z_retract_speed: 3.0
      }
    })

    apiService.getPrintJobStatus.mockResolvedValue({
      data: {
        status: 'ready',
        is_printing: false,
        has_error: false
      }
    })

    mockStatusStore = {
      isConnected: true, // Default to connected for most tests
      printData: {
        status: 'ready',
        is_printing: false,
        has_error: false
      },
      connectWebSocket: vi.fn(),
      disconnectWebSocket: vi.fn()
    }

    useStatusStore.mockReturnValue(mockStatusStore)
  })

  describe('Component Rendering', () => {
    it('renders the print view with all sections', () => {
      const wrapper = mount(PrintView)

      expect(wrapper.find('.print-view').exists()).toBe(true)
      expect(wrapper.find('.view-title').text()).toBe('Print Control')
      expect(wrapper.find('.status-card').exists()).toBe(true)
      expect(wrapper.find('.control-card').exists()).toBe(true)
      expect(wrapper.text()).toContain('Layer Parameters')
      expect(wrapper.text()).toContain('Layer Preview')
    })

    it('shows disconnected status when not connected', () => {
      mockStatusStore.isConnected = false
      const wrapper = mount(PrintView)

      expect(wrapper.find('.status-disconnected').exists()).toBe(true)
      expect(wrapper.find('.status-text').text()).toBe('Disconnected')
    })

    it('shows connected status when connected', () => {
      mockStatusStore.isConnected = true
      const wrapper = mount(PrintView)

      expect(wrapper.find('.status-connected').exists()).toBe(true)
      expect(wrapper.find('.status-text').text()).toBe('Connected')
    })

    it('shows disabled overlay when disconnected', () => {
      mockStatusStore.isConnected = false
      const wrapper = mount(PrintView)

      const overlays = wrapper.findAll('.disabled-overlay')
      expect(overlays.length).toBeGreaterThan(0)
      expect(wrapper.text()).toContain('Connect to recoater to configure layer parameters')
      expect(wrapper.text()).toContain('Connect to recoater to view layer preview')
    })
  })

  describe('Layer Parameters', () => {
    it('renders all parameter input fields', () => {
      mockStatusStore.isConnected = true
      const wrapper = mount(PrintView)

      expect(wrapper.find('#filling-id').exists()).toBe(true)
      expect(wrapper.find('#speed').exists()).toBe(true)
      expect(wrapper.find('#x-offset').exists()).toBe(true)
      expect(wrapper.find('.parameter-checkbox').exists()).toBe(true)
    })

    it('disables inputs when disconnected', () => {
      mockStatusStore.isConnected = false
      const wrapper = mount(PrintView)

      expect(wrapper.find('#filling-id').attributes('disabled')).toBeDefined()
      expect(wrapper.find('#speed').attributes('disabled')).toBeDefined()
      expect(wrapper.find('#x-offset').attributes('disabled')).toBeDefined()
      expect(wrapper.find('.parameter-checkbox').attributes('disabled')).toBeDefined()
    })

    it('enables inputs when connected', () => {
      mockStatusStore.isConnected = true
      const wrapper = mount(PrintView)

      expect(wrapper.find('#filling-id').attributes('disabled')).toBeUndefined()
      expect(wrapper.find('#speed').attributes('disabled')).toBeUndefined()
      expect(wrapper.find('#x-offset').attributes('disabled')).toBeUndefined()
      expect(wrapper.find('.parameter-checkbox').attributes('disabled')).toBeUndefined()
    })

    it('calls loadParameters when Load Current button is clicked', async () => {
      mockStatusStore.isConnected = true
      apiService.getLayerParameters.mockResolvedValue({
        data: {
          filling_id: 2,
          speed: 25.0,
          powder_saving: false,
          x_offset: 5.0
        }
      })

      const wrapper = mount(PrintView)
      // Clear the call from onMounted
      apiService.getLayerParameters.mockClear()

      const loadButton = wrapper.findAll('button').find(btn => btn.text().includes('Load Current'))

      await loadButton.trigger('click')
      await wrapper.vm.$nextTick()

      expect(apiService.getLayerParameters).toHaveBeenCalledOnce()
    })

    it('calls saveParameters when Save Parameters button is clicked', async () => {
      mockStatusStore.isConnected = true
      apiService.setLayerParameters.mockResolvedValue({ data: { success: true } })

      const wrapper = mount(PrintView)
      const saveButton = wrapper.findAll('button').find(btn => btn.text().includes('Save Parameters'))
      
      await saveButton.trigger('click')
      await wrapper.vm.$nextTick()

      expect(apiService.setLayerParameters).toHaveBeenCalledOnce()
    })

    it('validates parameters before saving', () => {
      mockStatusStore.isConnected = true
      const wrapper = mount(PrintView)

      // Set invalid speed
      wrapper.vm.layerParams.speed = 0
      expect(wrapper.vm.isParametersValid).toBe(false)

      // Set valid speed
      wrapper.vm.layerParams.speed = 30
      expect(wrapper.vm.isParametersValid).toBe(true)
    })

    it('disables save button when parameters are invalid', async () => {
      mockStatusStore.isConnected = true
      apiService.getLayerParameters.mockResolvedValue({
        data: {
          filling_id: 1,
          speed: 30.0,
          powder_saving: true,
          x_offset: 0.0
        }
      })

      const wrapper = mount(PrintView)
      await wrapper.vm.$nextTick()

      // Set invalid parameters
      wrapper.vm.layerParams.speed = 0
      await wrapper.vm.$nextTick()

      // Find save button by looking for the button that calls saveParameters
      const saveButton = wrapper.find('button[class*="btn-primary"]')
      expect(saveButton.exists()).toBe(true)
      expect(saveButton.attributes('disabled')).toBeDefined()
    })
  })

  describe('Layer Preview', () => {
    it('shows preview placeholder when no image is loaded', () => {
      mockStatusStore.isConnected = true
      const wrapper = mount(PrintView)

      expect(wrapper.find('.preview-placeholder').exists()).toBe(true)
      expect(wrapper.find('.preview-icon').exists()).toBe(true)
      expect(wrapper.text()).toContain('No preview available')
    })

    it('shows loading state when preview is loading', async () => {
      mockStatusStore.isConnected = true
      const wrapper = mount(PrintView)

      wrapper.vm.previewLoading = true
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.preview-loading').exists()).toBe(true)
      expect(wrapper.find('.loading-spinner').exists()).toBe(true)
      expect(wrapper.text()).toContain('Loading preview...')
    })

    it('calls loadPreview when Load Preview button is clicked', async () => {
      mockStatusStore.isConnected = true
      
      // Mock blob response
      const mockBlob = new Blob(['mock image data'], { type: 'image/png' })
      apiService.getLayerPreview.mockResolvedValue({ data: mockBlob })

      const wrapper = mount(PrintView)
      const previewButton = wrapper.findAll('button').find(btn => btn.text().includes('Load Preview'))
      
      await previewButton.trigger('click')
      await wrapper.vm.$nextTick()

      expect(apiService.getLayerPreview).toHaveBeenCalledOnce()
    })

    it('disables preview button when disconnected', () => {
      mockStatusStore.isConnected = false
      const wrapper = mount(PrintView)

      const previewButton = wrapper.findAll('button').find(btn => btn.text().includes('Load Preview'))
      expect(previewButton.attributes('disabled')).toBeDefined()
    })

    it('has preview source selector with correct options', () => {
      mockStatusStore.isConnected = true
      const wrapper = mount(PrintView)

      const previewSelect = wrapper.find('#preview-drum-select')
      expect(previewSelect.exists()).toBe(true)

      const options = previewSelect.findAll('option')
      expect(options).toHaveLength(4) // 1 layer option + 3 drum options
      expect(options[0].text()).toBe('Current Layer Configuration')
      expect(options[1].text()).toBe('Drum 0 Geometry')
      expect(options[2].text()).toBe('Drum 1 Geometry')
      expect(options[3].text()).toBe('Drum 2 Geometry')
    })

    it('calls getDrumGeometryPreview when drum geometry is selected', async () => {
      mockStatusStore.isConnected = true

      // Mock blob response
      const mockBlob = new Blob(['mock drum image data'], { type: 'image/png' })
      apiService.getDrumGeometryPreview.mockResolvedValue({ data: mockBlob })

      const wrapper = mount(PrintView)

      // Select drum 1 geometry
      const previewSelect = wrapper.find('#preview-drum-select')
      await previewSelect.setValue('drum-1')

      // Click load preview
      const previewButton = wrapper.findAll('button').find(btn => btn.text().includes('Load Preview'))
      await previewButton.trigger('click')
      await wrapper.vm.$nextTick()

      expect(apiService.getDrumGeometryPreview).toHaveBeenCalledWith(1)
    })
  })

  describe('Error Handling', () => {
    it('shows error message when parameter loading fails', async () => {
      mockStatusStore.isConnected = true
      apiService.getLayerParameters.mockRejectedValue(new Error('Connection failed'))

      const wrapper = mount(PrintView)
      await wrapper.vm.loadParameters()
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.message-error').exists()).toBe(true)
      expect(wrapper.text()).toContain('Failed to load layer parameters')
    })

    it('shows error message when parameter saving fails', async () => {
      mockStatusStore.isConnected = true
      apiService.setLayerParameters.mockRejectedValue(new Error('Save failed'))

      const wrapper = mount(PrintView)
      await wrapper.vm.saveParameters()
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.message-error').exists()).toBe(true)
      expect(wrapper.text()).toContain('Failed to save layer parameters')
    })

    it('shows error message when preview loading fails', async () => {
      mockStatusStore.isConnected = true
      apiService.getLayerPreview.mockRejectedValue(new Error('Preview failed'))

      const wrapper = mount(PrintView)
      await wrapper.vm.loadPreview()
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.message-error').exists()).toBe(true)
      expect(wrapper.text()).toContain('Failed to load preview')
    })
  })

  describe('Success Messages', () => {
    it('shows success message when parameters are loaded successfully', async () => {
      mockStatusStore.isConnected = true
      apiService.getLayerParameters.mockResolvedValue({
        data: { filling_id: 1, speed: 30.0, powder_saving: true, x_offset: 0.0 }
      })

      const wrapper = mount(PrintView)
      await wrapper.vm.loadParameters()
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.message-success').exists()).toBe(true)
      expect(wrapper.text()).toContain('Layer parameters loaded successfully')
    })

    it('shows success message when parameters are saved successfully', async () => {
      mockStatusStore.isConnected = true
      apiService.setLayerParameters.mockResolvedValue({ data: { success: true } })

      const wrapper = mount(PrintView)
      await wrapper.vm.saveParameters()
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.message-success').exists()).toBe(true)
      expect(wrapper.text()).toContain('Layer parameters saved successfully')
    })
  })

  describe('WebSocket Integration', () => {
    it('connects to WebSocket on mount', () => {
      mount(PrintView)
      expect(mockStatusStore.connectWebSocket).toHaveBeenCalledOnce()
    })
  })

  describe('Component Lifecycle', () => {
    it('cleans up object URLs on unmount', () => {
      // Mock URL.revokeObjectURL before mounting
      global.URL = global.URL || {}
      global.URL.revokeObjectURL = vi.fn()

      const wrapper = mount(PrintView)

      // Set a preview URL
      wrapper.vm.previewImageUrl = 'blob:mock-url'

      wrapper.unmount()

      expect(global.URL.revokeObjectURL).toHaveBeenCalledWith('blob:mock-url')
    })
  })

  describe('File Management', () => {
    beforeEach(() => {
      mockStatusStore.isConnected = true
    })

    it('renders file management section when connected', () => {
      const wrapper = mount(PrintView)

      expect(wrapper.find('.file-management-grid').exists()).toBe(true)
      expect(wrapper.find('.file-upload-section').exists()).toBe(true)
      expect(wrapper.find('.file-actions-section').exists()).toBe(true)
    })

    it('shows disabled overlay when disconnected', () => {
      mockStatusStore.isConnected = false
      const wrapper = mount(PrintView)

      const controlCards = wrapper.findAll('.control-card')
      const fileManagementCard = controlCards.find(card =>
        card.text().includes('File Management')
      )
      expect(fileManagementCard.find('.disabled-overlay').exists()).toBe(true)
    })

    it('handles file selection', async () => {
      const wrapper = mount(PrintView)

      const fileInput = wrapper.find('#file-input')
      expect(fileInput.exists()).toBe(true)

      // Mock file
      const mockFile = new File(['test'], 'test.png', { type: 'image/png' })

      // Simulate file selection
      Object.defineProperty(fileInput.element, 'files', {
        value: [mockFile],
        writable: false
      })

      await fileInput.trigger('change')

      expect(wrapper.vm.selectedFile).toBe(mockFile)
    })

    it('uploads file successfully', async () => {
      apiService.uploadDrumGeometry.mockResolvedValue({ data: { success: true } })

      const wrapper = mount(PrintView)

      // Set up file and drum selection
      const mockFile = new File(['test'], 'test.png', { type: 'image/png' })
      wrapper.vm.selectedFile = mockFile
      wrapper.vm.selectedDrumId = '1'

      await wrapper.vm.uploadFile()

      expect(apiService.uploadDrumGeometry).toHaveBeenCalledWith('1', mockFile)
      expect(wrapper.vm.selectedFile).toBe(null)
    })

    it('downloads file successfully', async () => {
      const mockBlob = new Blob(['test'], { type: 'image/png' })
      apiService.downloadDrumGeometry.mockResolvedValue({ data: mockBlob })

      // Mock DOM methods more carefully
      const mockLink = {
        href: '',
        download: '',
        click: vi.fn(),
        setAttribute: vi.fn(),
        style: {}
      }

      // Mock URL.createObjectURL
      const originalCreateObjectURL = global.URL.createObjectURL
      global.URL.createObjectURL = vi.fn(() => 'blob:mock-url')

      // Mock document.createElement to return our mock link
      const originalCreateElement = document.createElement
      document.createElement = vi.fn((tagName) => {
        if (tagName === 'a') {
          return mockLink
        }
        return originalCreateElement.call(document, tagName)
      })

      // Mock appendChild and removeChild to avoid DOM issues
      const originalAppendChild = document.body.appendChild
      const originalRemoveChild = document.body.removeChild
      document.body.appendChild = vi.fn()
      document.body.removeChild = vi.fn()

      const wrapper = mount(PrintView)
      wrapper.vm.actionDrumId = '1'

      await wrapper.vm.downloadFile()

      expect(apiService.downloadDrumGeometry).toHaveBeenCalledWith('1')
      expect(mockLink.click).toHaveBeenCalled()

      // Restore original methods
      document.createElement = originalCreateElement
      document.body.appendChild = originalAppendChild
      document.body.removeChild = originalRemoveChild
      global.URL.createObjectURL = originalCreateObjectURL
    })

    it('deletes file with confirmation', async () => {
      apiService.deleteDrumGeometry.mockResolvedValue({ data: { success: true } })

      // Mock confirm dialog
      global.confirm = vi.fn(() => true)

      const wrapper = mount(PrintView)
      wrapper.vm.actionDrumId = '1'

      await wrapper.vm.deleteFile()

      expect(global.confirm).toHaveBeenCalled()
      expect(apiService.deleteDrumGeometry).toHaveBeenCalledWith('1')
    })

    it('cancels file deletion when not confirmed', async () => {
      // Mock confirm dialog to return false
      global.confirm = vi.fn(() => false)

      const wrapper = mount(PrintView)
      wrapper.vm.actionDrumId = '1'

      await wrapper.vm.deleteFile()

      expect(global.confirm).toHaveBeenCalled()
      expect(apiService.deleteDrumGeometry).not.toHaveBeenCalled()
    })
  })

  describe('Job Management', () => {
    beforeEach(() => {
      // Reset all mocks
      vi.clearAllMocks()

      // Ensure clean DOM state
      if (document.body) {
        document.body.innerHTML = ''
      }

      mockStatusStore.isConnected = true
      mockStatusStore.printData = {
        job_status: {
          state: 'ready',
          is_printing: false,
          has_error: false
        }
      }
    })

    it('renders job management section when connected', () => {
      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      expect(wrapper.find('.job-status-section').exists()).toBe(true)
      expect(wrapper.find('.status-display').exists()).toBe(true)
      expect(wrapper.find('.job-controls').exists()).toBe(true)

      wrapper.unmount()
    })

    it('displays job status correctly', () => {
      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      expect(wrapper.vm.getJobStatusText()).toBe('Ready')
      expect(wrapper.vm.getJobStatusClass()).toBe('status-ready')
      expect(wrapper.vm.isPrinting).toBe(false)
      expect(wrapper.vm.hasJobError).toBe(false)

      wrapper.unmount()
    })

    it('displays printing status correctly', () => {
      mockStatusStore.printData.job_status = {
        state: 'printing',
        is_printing: true,
        has_error: false
      }

      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      expect(wrapper.vm.getJobStatusText()).toBe('Printing')
      expect(wrapper.vm.getJobStatusClass()).toBe('status-active')
      expect(wrapper.vm.isPrinting).toBe(true)

      wrapper.unmount()
    })

    it('displays error status correctly', () => {
      mockStatusStore.printData.job_status = {
        state: 'error',
        is_printing: false,
        has_error: true
      }

      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      expect(wrapper.vm.getJobStatusText()).toBe('Error')
      expect(wrapper.vm.getJobStatusClass()).toBe('status-error')
      expect(wrapper.vm.hasJobError).toBe(true)

      wrapper.unmount()
    })

    it('starts print job successfully', async () => {
      apiService.startPrintJob.mockResolvedValue({ data: { success: true } })

      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      await wrapper.vm.startPrintJob()

      expect(apiService.startPrintJob).toHaveBeenCalled()

      wrapper.unmount()
    })

    it('cancels print job with confirmation', async () => {
      apiService.cancelPrintJob.mockResolvedValue({ data: { success: true } })
      global.confirm = vi.fn(() => true)

      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      await wrapper.vm.cancelPrintJob()

      expect(global.confirm).toHaveBeenCalled()
      expect(apiService.cancelPrintJob).toHaveBeenCalled()

      wrapper.unmount()
    })

    it('does not cancel print job when not confirmed', async () => {
      global.confirm = vi.fn(() => false)

      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      await wrapper.vm.cancelPrintJob()

      expect(global.confirm).toHaveBeenCalled()
      expect(apiService.cancelPrintJob).not.toHaveBeenCalled()

      wrapper.unmount()
    })

    it('refreshes job status', async () => {
      apiService.getPrintJobStatus.mockResolvedValue({ data: { state: 'ready' } })

      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      await wrapper.vm.refreshJobStatus()

      expect(apiService.getPrintJobStatus).toHaveBeenCalled()

      wrapper.unmount()
    })

    it('disables start button when printing', () => {
      mockStatusStore.printData.job_status.is_printing = true

      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      const buttons = wrapper.findAll('button')
      const startButton = buttons.find(btn =>
        btn.text().includes('Start Print Job')
      )
      expect(startButton.element.disabled).toBe(true)

      wrapper.unmount()
    })

    it('disables cancel button when not printing', () => {
      mockStatusStore.printData.job_status.is_printing = false

      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      const buttons = wrapper.findAll('button')
      const cancelButton = buttons.find(btn =>
        btn.text().includes('Cancel Print Job')
      )
      expect(cancelButton.element.disabled).toBe(true)

      wrapper.unmount()
    })
  })

  describe('CLI File Management', () => {
    it('renders CLI upload section', () => {
      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      // Check for CLI section title
      expect(wrapper.text()).toContain('CLI Layer Preview')
      expect(wrapper.text()).toContain('Upload multi-layer CLI files')

      // Check for CLI file input
      const cliFileInput = wrapper.find('input[accept=".cli,application/octet-stream"]')
      expect(cliFileInput.exists()).toBe(true)

      wrapper.unmount()
    })

    it('handles CLI file selection', async () => {
      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      const cliFileInput = wrapper.find('input[accept=".cli,application/octet-stream"]')

      // Create a mock file
      const mockFile = new File(['test content'], 'test.cli', { type: 'application/octet-stream' })

      // Mock the file input change event
      Object.defineProperty(cliFileInput.element, 'files', {
        value: [mockFile],
        writable: false
      })

      await cliFileInput.trigger('change')
      await wrapper.vm.$nextTick()

      // Check that file is selected
      expect(wrapper.vm.selectedCliFile).toBe(mockFile)
      expect(wrapper.text()).toContain('test.cli')

      wrapper.unmount()
    })

    it('uploads CLI file successfully', async () => {
      // Mock successful API response
      apiService.uploadCliFile.mockResolvedValue({
        data: {
          success: true,
          message: 'CLI file uploaded successfully',
          file_id: 'test-file-id',
          total_layers: 5,
          file_size: 1024
        }
      })

      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      // Set a selected file
      const mockFile = new File(['test content'], 'test.cli', { type: 'application/octet-stream' })
      wrapper.vm.selectedCliFile = mockFile

      await wrapper.vm.$nextTick()

      // Find and click upload button
      const uploadButton = wrapper.findAll('button').find(btn =>
        btn.text().includes('Upload & Parse CLI')
      )
      expect(uploadButton.exists()).toBe(true)

      await uploadButton.trigger('click')
      await wrapper.vm.$nextTick()

      // Check that API was called
      expect(apiService.uploadCliFile).toHaveBeenCalledWith(mockFile)

      // Check that CLI info is set
      expect(wrapper.vm.cliFileInfo).toEqual({
        success: true,
        message: 'CLI file uploaded successfully',
        file_id: 'test-file-id',
        total_layers: 5,
        file_size: 1024
      })

      wrapper.unmount()
    })

    it('previews CLI layer successfully', async () => {
      // Mock successful API response
      const mockBlob = new Blob(['fake image data'], { type: 'image/png' })
      apiService.getCliLayerPreview.mockResolvedValue({
        data: mockBlob
      })

      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      // Set CLI file info (as if file was uploaded)
      wrapper.vm.cliFileInfo = {
        file_id: 'test-file-id',
        total_layers: 5
      }
      wrapper.vm.selectedLayerNum = 3

      await wrapper.vm.$nextTick()

      // Find and click preview button
      const previewButton = wrapper.findAll('button').find(btn =>
        btn.text().includes('Preview Layer')
      )
      expect(previewButton.exists()).toBe(true)

      await previewButton.trigger('click')
      await wrapper.vm.$nextTick()

      // Check that API was called with correct parameters
      expect(apiService.getCliLayerPreview).toHaveBeenCalledWith('test-file-id', 3)

      wrapper.unmount()
    })

    it('clears CLI file selection', async () => {
      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      // Set some CLI data
      const mockFile = new File(['test content'], 'test.cli', { type: 'application/octet-stream' })
      wrapper.vm.selectedCliFile = mockFile
      wrapper.vm.cliFileInfo = { file_id: 'test-id', total_layers: 3 }
      wrapper.vm.selectedLayerNum = 2

      await wrapper.vm.$nextTick()

      // Find and click clear button
      const clearButton = wrapper.findAll('button').find(btn =>
        btn.text().includes('Clear') && btn.element.closest('.cli-upload-section')
      )
      expect(clearButton.exists()).toBe(true)

      await clearButton.trigger('click')
      await wrapper.vm.$nextTick()

      // Check that data is cleared
      expect(wrapper.vm.selectedCliFile).toBe(null)
      expect(wrapper.vm.cliFileInfo).toBe(null)
      expect(wrapper.vm.selectedLayerNum).toBe(1)

      wrapper.unmount()
    })
  })
})
