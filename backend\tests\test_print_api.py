"""
Tests for Print API endpoints
============================

This module contains comprehensive tests for the print control API endpoints
including layer parameters and preview functionality.
"""

import pytest
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient
from io import BytesIO

from app.main import app
from services.recoater_client import RecoaterConnectionError, RecoaterAPIError
from services.cli_parser import CliParsingError, ParsedCliFile, CliLayer


class TestLayerParametersEndpoints:
    """Test layer parameters endpoints."""

    def test_get_layer_parameters_success(self, client, mock_recoater_client):
        """Test successful layer parameters retrieval."""
        # Mock successful response
        mock_recoater_client.get_layer_parameters.return_value = {
            "filling_id": 1,
            "speed": 30.0,
            "powder_saving": True,
            "x_offset": 0.0,
            "max_x_offset": 100.0
        }

        response = client.get("/api/v1/print/layer/parameters")

        assert response.status_code == 200
        data = response.json()
        assert data["filling_id"] == 1
        assert data["speed"] == 30.0
        assert data["powder_saving"] is True
        assert data["x_offset"] == 0.0
        assert data["max_x_offset"] == 100.0
        mock_recoater_client.get_layer_parameters.assert_called_once()

    def test_get_layer_parameters_connection_error(self, client, mock_recoater_client):
        """Test layer parameters retrieval with connection error."""
        mock_recoater_client.get_layer_parameters.side_effect = RecoaterConnectionError("Connection failed")

        response = client.get("/api/v1/print/layer/parameters")

        assert response.status_code == 503
        assert "Connection error" in response.json()["detail"]

    def test_get_layer_parameters_api_error(self, client, mock_recoater_client):
        """Test layer parameters retrieval with API error."""
        mock_recoater_client.get_layer_parameters.side_effect = RecoaterAPIError("API error")

        response = client.get("/api/v1/print/layer/parameters")

        assert response.status_code == 400
        assert "API error" in response.json()["detail"]

    def test_set_layer_parameters_success(self, client, mock_recoater_client):
        """Test successful layer parameters setting."""
        mock_recoater_client.set_layer_parameters.return_value = {"success": True}

        request_data = {
            "filling_id": 2,
            "speed": 25.0,
            "powder_saving": False,
            "x_offset": 5.0
        }

        response = client.put("/api/v1/print/layer/parameters", json=request_data)

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["message"] == "Layer parameters set successfully"
        assert data["parameters"]["filling_id"] == 2
        assert data["parameters"]["speed"] == 25.0
        assert data["parameters"]["powder_saving"] is False
        assert data["parameters"]["x_offset"] == 5.0

        mock_recoater_client.set_layer_parameters.assert_called_once_with(
            filling_id=2,
            speed=25.0,
            powder_saving=False,
            x_offset=5.0
        )

    def test_set_layer_parameters_minimal(self, client, mock_recoater_client):
        """Test setting layer parameters with minimal required fields."""
        mock_recoater_client.set_layer_parameters.return_value = {"success": True}

        request_data = {
            "filling_id": 1,
            "speed": 30.0
        }

        response = client.put("/api/v1/print/layer/parameters", json=request_data)

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["parameters"]["filling_id"] == 1
        assert data["parameters"]["speed"] == 30.0
        assert data["parameters"]["powder_saving"] is True  # Default value
        assert data["parameters"]["x_offset"] is None

        mock_recoater_client.set_layer_parameters.assert_called_once_with(
            filling_id=1,
            speed=30.0,
            powder_saving=True,
            x_offset=None
        )

    def test_set_layer_parameters_validation_error(self, client, mock_recoater_client):
        """Test layer parameters setting with validation error."""
        request_data = {
            "filling_id": 1,
            "speed": -5.0  # Invalid negative speed
        }

        response = client.put("/api/v1/print/layer/parameters", json=request_data)

        assert response.status_code == 422  # Validation error
        mock_recoater_client.set_layer_parameters.assert_not_called()

    def test_set_layer_parameters_connection_error(self, client, mock_recoater_client):
        """Test layer parameters setting with connection error."""
        mock_recoater_client.set_layer_parameters.side_effect = RecoaterConnectionError("Connection failed")

        request_data = {
            "filling_id": 1,
            "speed": 30.0
        }

        response = client.put("/api/v1/print/layer/parameters", json=request_data)

        assert response.status_code == 503
        assert "Connection error" in response.json()["detail"]

    def test_set_layer_parameters_api_error(self, client, mock_recoater_client):
        """Test layer parameters setting with API error."""
        mock_recoater_client.set_layer_parameters.side_effect = RecoaterAPIError("API error")

        request_data = {
            "filling_id": 1,
            "speed": 30.0
        }

        response = client.put("/api/v1/print/layer/parameters", json=request_data)

        assert response.status_code == 400
        assert "API error" in response.json()["detail"]


class TestLayerPreviewEndpoint:
    """Test layer preview endpoint."""

    def test_get_layer_preview_success(self, client, mock_recoater_client):
        """Test successful layer preview retrieval."""
        # Mock PNG image data
        mock_png_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde'
        mock_recoater_client.get_layer_preview.return_value = mock_png_data

        response = client.get("/api/v1/print/layer/preview")

        assert response.status_code == 200
        assert response.headers["content-type"] == "image/png"
        assert "layer_preview.png" in response.headers.get("content-disposition", "")
        assert response.content == mock_png_data
        mock_recoater_client.get_layer_preview.assert_called_once()

    def test_get_layer_preview_connection_error(self, client, mock_recoater_client):
        """Test layer preview retrieval with connection error."""
        mock_recoater_client.get_layer_preview.side_effect = RecoaterConnectionError("Connection failed")

        response = client.get("/api/v1/print/layer/preview")

        assert response.status_code == 503
        assert "Connection error" in response.json()["detail"]

    def test_get_layer_preview_api_error(self, client, mock_recoater_client):
        """Test layer preview retrieval with API error."""
        mock_recoater_client.get_layer_preview.side_effect = RecoaterAPIError("API error")

        response = client.get("/api/v1/print/layer/preview")

        assert response.status_code == 400
        assert "API error" in response.json()["detail"]


class TestDrumGeometryPreviewEndpoint:
    """Test drum geometry preview endpoint."""

    def test_get_drum_geometry_preview_success(self, client, mock_recoater_client):
        """Test successful drum geometry preview retrieval."""
        # Mock PNG image data
        mock_png_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x06\x00\x00\x00\x1f\x15\xc4\x89'
        mock_recoater_client.download_drum_geometry.return_value = mock_png_data

        response = client.get("/api/v1/print/drums/1/geometry/preview")

        assert response.status_code == 200
        assert response.headers["content-type"] == "image/png"
        assert "drum_1_preview.png" in response.headers.get("content-disposition", "")
        assert response.content == mock_png_data
        mock_recoater_client.download_drum_geometry.assert_called_once_with(1)

    def test_get_drum_geometry_preview_invalid_drum_id(self, client, mock_recoater_client):
        """Test drum geometry preview with invalid drum ID."""
        # Test with drum ID above maximum (only 3 drums: 0, 1, 2)
        response = client.get("/api/v1/print/drums/3/geometry/preview")
        assert response.status_code == 422  # Validation error

        # Test with negative drum ID
        response = client.get("/api/v1/print/drums/-1/geometry/preview")
        assert response.status_code == 422  # Validation error

    def test_get_drum_geometry_preview_connection_error(self, client, mock_recoater_client):
        """Test drum geometry preview with connection error."""
        mock_recoater_client.download_drum_geometry.side_effect = RecoaterConnectionError("Connection failed")

        response = client.get("/api/v1/print/drums/0/geometry/preview")

        assert response.status_code == 503
        assert "Connection error" in response.json()["detail"]


class TestMockStoragePersistence:
    """Test that mock storage correctly persists uploaded files."""

    def test_mock_client_storage_directly(self):
        """Test MockRecoaterClient storage functionality directly."""
        from backend.services.mock_recoater_client import MockRecoaterClient

        # Create a mock client instance
        mock_client = MockRecoaterClient("http://localhost:8080")

        # Test data
        test_data_0 = b"test file for drum 0"
        test_data_1 = b"test file for drum 1"

        # Initially, no files should be stored - should return default mock PNG
        default_data = mock_client.download_drum_geometry(0)
        assert len(default_data) > 0  # Should return default mock PNG

        # Upload files to different drums
        upload_result_0 = mock_client.upload_drum_geometry(0, test_data_0, "image/png")
        upload_result_1 = mock_client.upload_drum_geometry(1, test_data_1, "image/png")

        assert upload_result_0["success"] is True
        assert upload_result_1["success"] is True

        # Download should now return the uploaded data
        downloaded_0 = mock_client.download_drum_geometry(0)
        downloaded_1 = mock_client.download_drum_geometry(1)

        assert downloaded_0 == test_data_0
        assert downloaded_1 == test_data_1
        assert downloaded_0 != downloaded_1

        # Drum 2 should still return default mock PNG (no file uploaded)
        downloaded_2 = mock_client.download_drum_geometry(2)
        assert downloaded_2 == default_data

        # Delete file from drum 0
        delete_result = mock_client.delete_drum_geometry(0)
        assert delete_result["success"] is True

        # Drum 0 should now return default mock PNG again
        downloaded_0_after_delete = mock_client.download_drum_geometry(0)
        assert downloaded_0_after_delete == default_data
        assert downloaded_0_after_delete != test_data_0

        # Drum 1 should still have its file
        downloaded_1_after_delete = mock_client.download_drum_geometry(1)
        assert downloaded_1_after_delete == test_data_1


class TestPrintJobEndpoints:
    """Test print job management endpoints."""

    def test_start_print_job_success(self, client, mock_recoater_client):
        """Test successful print job start."""
        mock_recoater_client.start_print_job.return_value = {
            "success": True,
            "job_id": "job_12345",
            "status": "started"
        }

        response = client.post("/api/v1/print/job")

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["status"] == "started"
        assert data["job_id"] == "job_12345"
        mock_recoater_client.start_print_job.assert_called_once()

    def test_start_print_job_connection_error(self, client, mock_recoater_client):
        """Test print job start with connection error."""
        mock_recoater_client.start_print_job.side_effect = RecoaterConnectionError("Connection failed")

        response = client.post("/api/v1/print/job")

        assert response.status_code == 503
        assert "Connection error" in response.json()["detail"]

    def test_start_print_job_api_error(self, client, mock_recoater_client):
        """Test print job start with API error (conflict)."""
        mock_recoater_client.start_print_job.side_effect = RecoaterAPIError("Already printing")

        response = client.post("/api/v1/print/job")

        assert response.status_code == 409
        assert "Cannot start print job" in response.json()["detail"]

    def test_cancel_print_job_success(self, client, mock_recoater_client):
        """Test successful print job cancellation."""
        mock_recoater_client.cancel_print_job.return_value = {
            "success": True,
            "status": "cancelled"
        }

        response = client.delete("/api/v1/print/job")

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["status"] == "cancelled"
        assert data["job_id"] is None
        mock_recoater_client.cancel_print_job.assert_called_once()

    def test_cancel_print_job_connection_error(self, client, mock_recoater_client):
        """Test print job cancellation with connection error."""
        mock_recoater_client.cancel_print_job.side_effect = RecoaterConnectionError("Connection failed")

        response = client.delete("/api/v1/print/job")

        assert response.status_code == 503
        assert "Connection error" in response.json()["detail"]

    def test_cancel_print_job_api_error(self, client, mock_recoater_client):
        """Test print job cancellation with API error."""
        mock_recoater_client.cancel_print_job.side_effect = RecoaterAPIError("No job running")

        response = client.delete("/api/v1/print/job")

        assert response.status_code == 400
        assert "API error" in response.json()["detail"]


class TestPrintJobStatusEndpoints:
    """Test print job status endpoints."""

    def test_get_print_job_status_success(self, client, mock_recoater_client):
        """Test successful print job status retrieval."""
        # Mock successful response
        mock_recoater_client.get_print_job_status.return_value = {
            "state": "ready",
            "is_printing": False,
            "has_error": False
        }

        response = client.get("/api/v1/print/job/status")

        assert response.status_code == 200
        data = response.json()
        assert data["state"] == "ready"
        assert data["is_printing"] is False
        assert data["has_error"] is False
        mock_recoater_client.get_print_job_status.assert_called_once()

    def test_get_print_job_status_printing(self, client, mock_recoater_client):
        """Test print job status when printing."""
        # Mock printing state response
        mock_recoater_client.get_print_job_status.return_value = {
            "state": "printing",
            "is_printing": True,
            "has_error": False
        }

        response = client.get("/api/v1/print/job/status")

        assert response.status_code == 200
        data = response.json()
        assert data["state"] == "printing"
        assert data["is_printing"] is True
        assert data["has_error"] is False

    def test_get_print_job_status_error(self, client, mock_recoater_client):
        """Test print job status when error state."""
        # Mock error state response
        mock_recoater_client.get_print_job_status.return_value = {
            "state": "error",
            "is_printing": False,
            "has_error": True
        }

        response = client.get("/api/v1/print/job/status")

        assert response.status_code == 200
        data = response.json()
        assert data["state"] == "error"
        assert data["is_printing"] is False
        assert data["has_error"] is True

    def test_get_print_job_status_connection_error(self, client, mock_recoater_client):
        """Test print job status with connection error."""
        mock_recoater_client.get_print_job_status.side_effect = RecoaterConnectionError("Connection failed")

        response = client.get("/api/v1/print/job/status")

        assert response.status_code == 503
        assert "Connection error" in response.json()["detail"]

    def test_get_print_job_status_api_error(self, client, mock_recoater_client):
        """Test print job status with API error."""
        mock_recoater_client.get_print_job_status.side_effect = RecoaterAPIError("API error")

        response = client.get("/api/v1/print/job/status")

        assert response.status_code == 400
        assert "API error" in response.json()["detail"]


class TestFileManagementEndpoints:
    """Test file management endpoints."""

    def test_upload_drum_geometry_success(self, client, mock_recoater_client):
        """Test successful drum geometry file upload."""
        # Mock successful response
        mock_recoater_client.upload_drum_geometry.return_value = {
            "success": True,
            "drum_id": 1,
            "file_size": 1024,
            "content_type": "image/png"
        }

        # Create test file
        test_file_content = b"fake PNG content"
        files = {"file": ("test.png", BytesIO(test_file_content), "image/png")}

        response = client.post("/api/v1/print/drums/1/geometry", files=files)

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["drum_id"] == 1
        assert data["file_size"] == len(test_file_content)
        assert "uploaded successfully" in data["message"]
        mock_recoater_client.upload_drum_geometry.assert_called_once()

    def test_upload_drum_geometry_cli_file(self, client, mock_recoater_client):
        """Test uploading CLI file to drum."""
        # Mock successful response
        mock_recoater_client.upload_drum_geometry.return_value = {
            "success": True,
            "drum_id": 2,
            "file_size": 2048,
            "content_type": "application/octet-stream"
        }

        # Create test CLI file
        test_file_content = b"fake CLI content"
        files = {"file": ("test.cli", BytesIO(test_file_content), "application/octet-stream")}

        response = client.post("/api/v1/print/drums/2/geometry", files=files)

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["drum_id"] == 2
        assert data["content_type"] == "application/octet-stream"

    def test_upload_drum_geometry_connection_error(self, client, mock_recoater_client):
        """Test drum geometry upload with connection error."""
        mock_recoater_client.upload_drum_geometry.side_effect = RecoaterConnectionError("Connection failed")

        test_file_content = b"fake content"
        files = {"file": ("test.png", BytesIO(test_file_content), "image/png")}

        response = client.post("/api/v1/print/drums/1/geometry", files=files)

        assert response.status_code == 503
        assert "Connection error" in response.json()["detail"]

    def test_upload_drum_geometry_api_error(self, client, mock_recoater_client):
        """Test drum geometry upload with API error."""
        mock_recoater_client.upload_drum_geometry.side_effect = RecoaterAPIError("Invalid file format")

        test_file_content = b"fake content"
        files = {"file": ("test.txt", BytesIO(test_file_content), "text/plain")}

        response = client.post("/api/v1/print/drums/1/geometry", files=files)

        assert response.status_code == 400
        assert "API error" in response.json()["detail"]

    def test_download_drum_geometry_success(self, client, mock_recoater_client):
        """Test successful drum geometry file download."""
        # Mock PNG image data
        mock_png_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x06\x00\x00\x00\x1f\x15\xc4\x89'
        mock_recoater_client.download_drum_geometry.return_value = mock_png_data

        response = client.get("/api/v1/print/drums/1/geometry")

        assert response.status_code == 200
        assert response.headers["content-type"] == "image/png"
        assert "attachment" in response.headers["content-disposition"]
        assert "drum_1_geometry.png" in response.headers["content-disposition"]
        assert response.content == mock_png_data
        mock_recoater_client.download_drum_geometry.assert_called_once_with(1)

    def test_download_drum_geometry_connection_error(self, client, mock_recoater_client):
        """Test drum geometry download with connection error."""
        mock_recoater_client.download_drum_geometry.side_effect = RecoaterConnectionError("Connection failed")

        response = client.get("/api/v1/print/drums/1/geometry")

        assert response.status_code == 503
        assert "Connection error" in response.json()["detail"]

    def test_download_drum_geometry_api_error(self, client, mock_recoater_client):
        """Test drum geometry download with API error."""
        mock_recoater_client.download_drum_geometry.side_effect = RecoaterAPIError("No geometry found")

        response = client.get("/api/v1/print/drums/1/geometry")

        assert response.status_code == 400
        assert "API error" in response.json()["detail"]

    def test_delete_drum_geometry_success(self, client, mock_recoater_client):
        """Test successful drum geometry file deletion."""
        # Mock successful response
        mock_recoater_client.delete_drum_geometry.return_value = {
            "success": True,
            "drum_id": 1,
            "message": "Geometry file deleted successfully"
        }

        response = client.delete("/api/v1/print/drums/1/geometry")

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["drum_id"] == 1
        assert "deleted successfully" in data["message"]
        mock_recoater_client.delete_drum_geometry.assert_called_once_with(1)

    def test_delete_drum_geometry_connection_error(self, client, mock_recoater_client):
        """Test drum geometry deletion with connection error."""
        mock_recoater_client.delete_drum_geometry.side_effect = RecoaterConnectionError("Connection failed")

        response = client.delete("/api/v1/print/drums/1/geometry")

        assert response.status_code == 503
        assert "Connection error" in response.json()["detail"]

    def test_delete_drum_geometry_api_error(self, client, mock_recoater_client):
        """Test drum geometry deletion with API error."""
        mock_recoater_client.delete_drum_geometry.side_effect = RecoaterAPIError("No geometry to delete")

        response = client.delete("/api/v1/print/drums/1/geometry")

        assert response.status_code == 400
        assert "API error" in response.json()["detail"]


class TestCliFileEndpoints:
    """Test CLI file management endpoints."""

    @patch('app.api.print.CliParserService')
    def test_upload_cli_file_success(self, mock_parser_class, client):
        """Test successful CLI file upload and parsing."""
        # Create mock parser instance
        mock_parser = Mock()
        mock_parser_class.return_value = mock_parser

        # Create mock parsed data
        mock_layer = CliLayer(z_height=0.1, polylines=[], hatches=[])
        mock_parsed_data = ParsedCliFile(
            header_lines=["$$HEADEREND"],
            is_aligned=False,
            layers=[mock_layer, mock_layer]  # 2 layers
        )
        mock_parser.parse.return_value = mock_parsed_data

        # Create test file
        test_file_content = b"test cli file content"
        files = {"file": ("test.cli", BytesIO(test_file_content), "application/octet-stream")}

        response = client.post("/api/v1/print/cli/upload", files=files)

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "test.cli" in data["message"]
        assert "file_id" in data
        assert data["total_layers"] == 2
        assert data["file_size"] == len(test_file_content)

        # Verify parser was called correctly
        mock_parser_class.assert_called_once()
        mock_parser.parse.assert_called_once_with(test_file_content)

    def test_upload_cli_file_invalid_extension(self, client):
        """Test CLI file upload with invalid file extension."""
        files = {"file": ("test.txt", BytesIO(b"content"), "text/plain")}

        response = client.post("/api/v1/print/cli/upload", files=files)

        assert response.status_code == 400
        assert "Only .cli files are supported" in response.json()["detail"]

    def test_upload_cli_file_empty_file(self, client):
        """Test CLI file upload with empty file."""
        files = {"file": ("test.cli", BytesIO(b""), "application/octet-stream")}

        response = client.post("/api/v1/print/cli/upload", files=files)

        assert response.status_code == 400
        assert "Uploaded file is empty" in response.json()["detail"]

    @patch('app.api.print.CliParserService')
    def test_upload_cli_file_parsing_error(self, mock_parser_class, client):
        """Test CLI file upload with parsing error."""
        mock_parser = Mock()
        mock_parser_class.return_value = mock_parser
        mock_parser.parse.side_effect = CliParsingError("Invalid CLI format")

        files = {"file": ("test.cli", BytesIO(b"invalid content"), "application/octet-stream")}

        response = client.post("/api/v1/print/cli/upload", files=files)

        assert response.status_code == 400
        assert "CLI parsing error" in response.json()["detail"]
        assert "Invalid CLI format" in response.json()["detail"]

    @patch('app.api.print.CliParserService')
    def test_get_cli_layer_preview_success(self, mock_parser_class, client):
        """Test successful CLI layer preview."""
        # First upload a file to get it in cache
        mock_parser = Mock()
        mock_parser_class.return_value = mock_parser

        mock_layer1 = CliLayer(z_height=0.1, polylines=[], hatches=[])
        mock_layer2 = CliLayer(z_height=0.2, polylines=[], hatches=[])
        mock_parsed_data = ParsedCliFile(
            header_lines=["$$HEADEREND"],
            is_aligned=False,
            layers=[mock_layer1, mock_layer2]
        )
        mock_parser.parse.return_value = mock_parsed_data
        mock_parser.render_layer_to_png.return_value = b"fake png data"

        # Upload file first
        files = {"file": ("test.cli", BytesIO(b"test content"), "application/octet-stream")}
        upload_response = client.post("/api/v1/print/cli/upload", files=files)
        file_id = upload_response.json()["file_id"]

        # Test layer preview
        response = client.get(f"/api/v1/print/cli/{file_id}/layer/1/preview")

        assert response.status_code == 200
        assert response.headers["content-type"] == "image/png"
        assert response.content == b"fake png data"

        # Verify render was called with correct layer
        mock_parser.render_layer_to_png.assert_called_once_with(mock_layer1)

    def test_get_cli_layer_preview_file_not_found(self, client):
        """Test CLI layer preview with non-existent file ID."""
        response = client.get("/api/v1/print/cli/nonexistent-id/layer/1/preview")

        assert response.status_code == 404
        assert "CLI file not found" in response.json()["detail"]

    @patch('app.api.print.CliParserService')
    def test_get_cli_layer_preview_invalid_layer_number(self, mock_parser_class, client):
        """Test CLI layer preview with invalid layer number."""
        # Upload file first
        mock_parser = Mock()
        mock_parser_class.return_value = mock_parser

        mock_layer = CliLayer(z_height=0.1, polylines=[], hatches=[])
        mock_parsed_data = ParsedCliFile(
            header_lines=["$$HEADEREND"],
            is_aligned=False,
            layers=[mock_layer]  # Only 1 layer
        )
        mock_parser.parse.return_value = mock_parsed_data

        files = {"file": ("test.cli", BytesIO(b"test content"), "application/octet-stream")}
        upload_response = client.post("/api/v1/print/cli/upload", files=files)
        file_id = upload_response.json()["file_id"]

        # Test with layer number too high
        response = client.get(f"/api/v1/print/cli/{file_id}/layer/5/preview")

        assert response.status_code == 400
        assert "Invalid layer number" in response.json()["detail"]

    @patch('app.api.print.CliParserService')
    def test_send_cli_layer_to_drum_success(self, mock_parser_class, client, mock_recoater_client):
        """Test successful CLI layer send to drum."""
        # Upload file first
        mock_parser = Mock()
        mock_parser_class.return_value = mock_parser

        mock_layer = CliLayer(z_height=0.1, polylines=[], hatches=[])
        mock_parsed_data = ParsedCliFile(
            header_lines=["$$HEADEREND"],
            is_aligned=False,
            layers=[mock_layer]
        )
        mock_parser.parse.return_value = mock_parsed_data

        # Mock CLI generation
        mock_cli_data = b"mock cli data"
        mock_parser.generate_single_layer_cli.return_value = mock_cli_data

        # Mock recoater client upload
        mock_recoater_client.upload_drum_geometry.return_value = {
            "success": True,
            "drum_id": 1,
            "file_size": len(mock_cli_data)
        }

        # Upload CLI file
        test_file_content = b"fake cli content"
        files = {"file": ("test.cli", BytesIO(test_file_content), "application/octet-stream")}
        upload_response = client.post("/api/v1/print/cli/upload", files=files)
        assert upload_response.status_code == 200

        file_id = upload_response.json()["file_id"]

        # Send layer to drum
        response = client.post(f"/api/v1/print/cli/{file_id}/layer/1/send/1")

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["file_id"] == file_id
        assert data["layer_num"] == 1
        assert data["drum_id"] == 1
        assert data["layer_z_height"] == 0.1
        assert data["data_size"] == len(mock_cli_data)

        # Verify CLI generation was called correctly
        mock_parser.generate_single_layer_cli.assert_called_once_with(
            layer=mock_layer,
            header_lines=["$$HEADEREND"],
            is_aligned=False
        )

        # Verify upload was called correctly
        mock_recoater_client.upload_drum_geometry.assert_called_once_with(
            drum_id=1,
            file_data=mock_cli_data,
            content_type="application/octet-stream"
        )

    def test_send_cli_layer_to_drum_file_not_found(self, client):
        """Test CLI layer send with non-existent file ID."""
        response = client.post("/api/v1/print/cli/nonexistent-id/layer/1/send/1")

        assert response.status_code == 404
        assert "CLI file not found" in response.json()["detail"]

    @patch('app.api.print.CliParserService')
    def test_send_cli_layer_to_drum_invalid_layer_number(self, mock_parser_class, client):
        """Test CLI layer send with invalid layer number."""
        # Upload file first
        mock_parser = Mock()
        mock_parser_class.return_value = mock_parser

        mock_layer = CliLayer(z_height=0.1, polylines=[], hatches=[])
        mock_parsed_data = ParsedCliFile(
            header_lines=["$$HEADEREND"],
            is_aligned=False,
            layers=[mock_layer]  # Only 1 layer
        )
        mock_parser.parse.return_value = mock_parsed_data

        # Upload CLI file
        test_file_content = b"fake cli content"
        files = {"file": ("test.cli", BytesIO(test_file_content), "application/octet-stream")}
        upload_response = client.post("/api/v1/print/cli/upload", files=files)
        assert upload_response.status_code == 200

        file_id = upload_response.json()["file_id"]

        # Test invalid layer number (too high)
        response = client.post(f"/api/v1/print/cli/{file_id}/layer/2/send/1")

        assert response.status_code == 400
        assert "Invalid layer number" in response.json()["detail"]

    @patch('app.api.print.CliParserService')
    def test_send_cli_layer_to_drum_connection_error(self, mock_parser_class, client, mock_recoater_client):
        """Test CLI layer send with connection error."""
        # Upload file first
        mock_parser = Mock()
        mock_parser_class.return_value = mock_parser

        mock_layer = CliLayer(z_height=0.1, polylines=[], hatches=[])
        mock_parsed_data = ParsedCliFile(
            header_lines=["$$HEADEREND"],
            is_aligned=False,
            layers=[mock_layer]
        )
        mock_parser.parse.return_value = mock_parsed_data

        # Mock CLI generation
        mock_cli_data = b"mock cli data"
        mock_parser.generate_single_layer_cli.return_value = mock_cli_data

        # Mock recoater client connection error
        mock_recoater_client.upload_drum_geometry.side_effect = RecoaterConnectionError("Connection failed")

        # Upload CLI file
        test_file_content = b"fake cli content"
        files = {"file": ("test.cli", BytesIO(test_file_content), "application/octet-stream")}
        upload_response = client.post("/api/v1/print/cli/upload", files=files)
        assert upload_response.status_code == 200

        file_id = upload_response.json()["file_id"]

        # Send layer to drum
        response = client.post(f"/api/v1/print/cli/{file_id}/layer/1/send/1")

        assert response.status_code == 503
        assert "Connection error" in response.json()["detail"]


class TestCliParserService:
    """Test CLI parser service functionality."""

    def test_generate_single_layer_cli_basic(self):
        """Test basic CLI generation for a single layer."""
        from services.cli_parser import CliParserService, CliLayer, Point, Polyline, Hatch

        parser = CliParserService()

        # Create a simple layer with polylines and hatches
        layer = CliLayer(
            z_height=0.5,
            polylines=[
                Polyline(
                    part_id=1,
                    direction=0,
                    points=[Point(x=0.0, y=0.0), Point(x=10.0, y=10.0)]
                )
            ],
            hatches=[
                Hatch(
                    group_id=1,
                    lines=[(Point(x=1.0, y=1.0), Point(x=9.0, y=9.0))]
                )
            ]
        )

        # Generate CLI data
        cli_data = parser.generate_single_layer_cli(layer)

        # Verify we got bytes back
        assert isinstance(cli_data, bytes)
        assert len(cli_data) > 0

        # Verify header is present
        assert b"$$HEADEREND" in cli_data

    def test_generate_single_layer_cli_with_custom_header(self):
        """Test CLI generation with custom header."""
        from services.cli_parser import CliParserService, CliLayer

        parser = CliParserService()

        layer = CliLayer(z_height=1.0, polylines=[], hatches=[])
        custom_header = ["$$UNITS/1", "$$HEADEREND"]

        cli_data = parser.generate_single_layer_cli(layer, header_lines=custom_header)

        # Verify custom header is present
        assert b"$$UNITS/1" in cli_data
        assert b"$$HEADEREND" in cli_data

    def test_generate_single_layer_cli_empty_layer(self):
        """Test CLI generation for an empty layer."""
        from services.cli_parser import CliParserService, CliLayer

        parser = CliParserService()

        layer = CliLayer(z_height=2.0, polylines=[], hatches=[])

        cli_data = parser.generate_single_layer_cli(layer)

        # Should still generate valid CLI data with header and layer command
        assert isinstance(cli_data, bytes)
        assert len(cli_data) > 0
        assert b"$$HEADEREND" in cli_data
